<template>
  <div class="flex flex-col h-full bg-background">
    <!-- 数据可视化顶部标题栏 -->
    <div class="border-b bg-card px-6 py-3 hover:bg-muted/30 transition-colors duration-300">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-xl font-bold text-foreground">数据可视化分析</h1>
          <p class="text-xs text-muted-foreground mt-1">银行业务数据图表分析与趋势监控</p>
        </div>
        <div class="flex items-center space-x-2">
          <!-- 时间范围选择器 -->
          <select
            v-model="selectedTimeRange"
            class="px-3 py-1 text-sm border rounded-md bg-background hover:bg-muted/50 transition-colors"
            @change="handleTimeRangeChange"
          >
            <option value="1h">近1小时</option>
            <option value="24h">近24小时</option>
            <option value="7d">近7天</option>
            <option value="30d">近30天</option>
          </select>

          <!-- 刷新按钮 -->
          <Button
            variant="outline"
            size="sm"
            :disabled="isRefreshing"
            class="hover:scale-105 transition-all duration-300 hover:shadow-md"
            @click="refreshData"
          >
            <RefreshCw :class="['w-3 h-3 mr-1', isRefreshing ? 'animate-spin' : '']" />
            刷新
          </Button>

          <!-- 导出按钮 -->
          <Button
            variant="outline"
            size="sm"
            class="hover:scale-105 transition-all duration-300 hover:shadow-md"
            @click="exportData"
          >
            <Download class="w-3 h-3 mr-1" />
            导出
          </Button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 p-4 space-y-4 overflow-auto">
      <!-- 关键指标概览 -->
      <div class="grid grid-cols-4 gap-4">
        <Card
          v-for="metric in keyMetrics"
          :key="metric.id"
          class="cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 hover:border-primary/50 group hover:bg-primary/5"
          @click="handleMetricClick(metric)"
        >
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-1 pt-3">
            <CardTitle class="text-sm font-medium group-hover:text-primary transition-colors">
              {{ metric.title }}
            </CardTitle>
            <component
              :is="metric.icon"
              :class="[
                'h-4 w-4 transition-all duration-300 group-hover:scale-110',
                metric.iconColor
              ]"
            />
          </CardHeader>
          <CardContent class="pb-3">
            <div class="text-xl font-bold group-hover:text-primary transition-colors mb-1">
              {{ metric.value }}
            </div>
            <div class="flex items-center text-xs text-muted-foreground">
              <component
                :is="
                  metric.trend === 'up'
                    ? TrendingUp
                    : metric.trend === 'down'
                      ? TrendingDown
                      : Minus
                "
                :class="['h-3 w-3 mr-1 transition-all duration-300', getTrendColor(metric.trend)]"
              />
              <span :class="getTrendColor(metric.trend)">{{ metric.change }}</span>
              <span class="ml-1">较上期</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 图表展示区域 -->
      <div class="grid grid-cols-2 gap-4">
        <!-- 交易趋势图表 -->
        <Card class="hover:shadow-md transition-shadow duration-300">
          <CardContent class="p-4">
            <TransactionTrendChart
              title="交易趋势分析"
              subtitle="实时交易量监控"
              chart-height="300px"
              :auto-refresh="true"
              :refresh-interval="120000"
            />
          </CardContent>
        </Card>

        <!-- 风险分布图表 -->
        <Card class="hover:shadow-md transition-shadow duration-300">
          <CardContent class="p-4">
            <RiskDistributionChart
              title="风险等级分布"
              subtitle="交易风险分析统计"
              chart-height="400px"
              :auto-refresh="true"
              :refresh-interval="150000"
            />
          </CardContent>
        </Card>
      </div>

      <!-- 业务数据报表区域 -->
      <div class="grid grid-cols-3 gap-4">
        <!-- 地域分布图表 -->
        <Card class="hover:shadow-md transition-shadow duration-300">
          <CardHeader>
            <CardTitle class="flex items-center">
              <BarChart3 class="w-5 h-5 mr-2" />
              地域分布统计
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer
              title="地域分布"
              chart-type="bar"
              chart-height="250px"
              :chart-data="regionDistributionData"
              :show-time-range="false"
            />
          </CardContent>
        </Card>

        <!-- 时段分析图表 -->
        <Card class="hover:shadow-md transition-shadow duration-300">
          <CardHeader>
            <CardTitle class="flex items-center">
              <Clock class="w-5 h-5 mr-2" />
              时段分析
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer
              title="时段分析"
              chart-type="line"
              chart-height="250px"
              :chart-data="timeAnalysisData"
              :show-time-range="false"
            />
          </CardContent>
        </Card>

        <!-- 业务类型分布 -->
        <Card class="hover:shadow-md transition-shadow duration-300">
          <CardHeader>
            <CardTitle class="flex items-center">
              <Target class="w-5 h-5 mr-2" />
              业务类型分布
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer
              title="业务类型"
              chart-type="pie"
              chart-height="250px"
              :chart-data="businessTypeData"
              :show-time-range="false"
            />
          </CardContent>
        </Card>
      </div>

      <!-- 实时数据流监控 -->
      <Card class="hover:shadow-md transition-shadow duration-300">
        <CardContent class="p-4">
          <RealtimeDataStream
            title="实时数据流监控"
            subtitle="系统性能与交易数据实时监控"
            chart-height="400px"
            :max-data-points="30"
          />
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import {
  TrendingUp,
  TrendingDown,
  Minus,
  RefreshCw,
  Download,
  LineChart,
  PieChart,
  BarChart3,
  Clock,
  Target,
  Activity,
  DollarSign,
  AlertTriangle,
  Users,
  Database
} from 'lucide-vue-next'

// shadcn-vue 组件导入
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

// 导入图表容器组件
import ChartContainer from '@/components/ChartContainer.vue'

// 导入专门的图表组件
import TransactionTrendChart from '@/components/charts/TransactionTrendChart.vue'
import RiskDistributionChart from '@/components/charts/RiskDistributionChart.vue'
import RealtimeDataStream from '@/components/charts/RealtimeDataStream.vue'

// 导入性能监控工具
import { performanceMonitor, createDebounce, batchUpdate } from '@/utils/performance'

/**
 * 数据可视化相关接口定义
 */
interface MetricItem {
  id: string
  title: string
  value: string
  change: string
  trend: 'up' | 'down' | 'neutral'
  icon: any
  iconColor: string
}

/**
 * 响应式数据
 */
const selectedTimeRange = ref('24h')
const isRefreshing = ref(false)

/**
 * 关键指标数据
 */
const keyMetrics = ref<MetricItem[]>([
  {
    id: 'total-transactions',
    title: '总交易量',
    value: '¥12.8M',
    change: '+15.2%',
    trend: 'up',
    icon: DollarSign,
    iconColor: 'text-green-500'
  },
  {
    id: 'risk-alerts',
    title: '风险告警',
    value: '23',
    change: '-8.1%',
    trend: 'down',
    icon: AlertTriangle,
    iconColor: 'text-orange-500'
  },
  {
    id: 'active-users',
    title: '活跃用户',
    value: '1,847',
    change: '+12.3%',
    trend: 'up',
    icon: Users,
    iconColor: 'text-blue-500'
  },
  {
    id: 'data-processed',
    title: '数据处理量',
    value: '2.1TB',
    change: '+5.7%',
    trend: 'up',
    icon: Database,
    iconColor: 'text-purple-500'
  }
])

/**
 * 模拟图表数据
 */
const transactionTrendData = ref({
  labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
  datasets: [
    {
      label: '交易量',
      data: [120, 190, 300, 500, 200, 300]
    }
  ]
})

const riskDistributionData = ref({
  labels: ['低风险', '中风险', '高风险'],
  datasets: [
    {
      data: [65, 25, 10]
    }
  ]
})

const regionDistributionData = ref({
  labels: ['北京', '上海', '广州', '深圳', '杭州'],
  datasets: [
    {
      label: '交易笔数',
      data: [450, 380, 320, 290, 180]
    }
  ]
})

const timeAnalysisData = ref({
  labels: ['9:00', '12:00', '15:00', '18:00', '21:00'],
  datasets: [
    {
      label: '交易频次',
      data: [65, 85, 120, 95, 45]
    }
  ]
})

const businessTypeData = ref({
  labels: ['转账', '支付', '理财', '贷款', '其他'],
  datasets: [
    {
      data: [40, 30, 15, 10, 5]
    }
  ]
})

const realtimeTransactionData = ref({
  labels: ['实时'],
  datasets: [
    {
      label: '交易量',
      data: [Math.floor(Math.random() * 100)]
    }
  ]
})

const realtimeAlertData = ref({
  labels: ['告警'],
  datasets: [
    {
      label: '告警数',
      data: [Math.floor(Math.random() * 20)]
    }
  ]
})

/**
 * 工具函数
 */

/**
 * 获取趋势颜色
 */
const getTrendColor = (trend: 'up' | 'down' | 'neutral') => {
  switch (trend) {
    case 'up':
      return 'text-green-500'
    case 'down':
      return 'text-red-500'
    default:
      return 'text-gray-500'
  }
}

/**
 * 事件处理函数
 */

/**
 * 处理时间范围变化
 */
const handleTimeRangeChange = () => {
  console.log('时间范围变更为:', selectedTimeRange.value)
  refreshData()
}

/**
 * 防抖刷新数据函数
 * 避免用户频繁点击导致的性能问题
 */
let refreshTimeout: NodeJS.Timeout

const refreshData = async () => {
  // 防抖处理
  if (refreshTimeout) {
    clearTimeout(refreshTimeout)
  }

  if (isRefreshing.value) {
    console.log('数据正在刷新中，请稍候...')
    return
  }

  isRefreshing.value = true
  console.log('正在刷新数据可视化数据...')

  try {
    // 使用 Promise 模拟异步数据加载
    await new Promise(resolve => {
      refreshTimeout = setTimeout(() => {
        // 批量更新数据
        updateRealtimeData()

        // 更新其他静态数据（降低频率）
        if (Math.random() < 0.5) {
          // 更新地域分布数据
          regionDistributionData.value.datasets[0].data =
            regionDistributionData.value.datasets[0].data.map(() =>
              Math.floor(Math.random() * 200 + 200)
            )

          // 更新时段分析数据
          timeAnalysisData.value.datasets[0].data =
            timeAnalysisData.value.datasets[0].data.map(() =>
              Math.floor(Math.random() * 80 + 40)
            )
        }

        resolve(true)
      }, 800) // 减少模拟延迟
    })

    console.log('数据刷新完成')
  } catch (error) {
    console.error('数据刷新失败:', error)
  } finally {
    isRefreshing.value = false
  }
}

/**
 * 导出数据
 */
const exportData = () => {
  console.log('导出数据可视化报表')
  // 这里可以实现数据导出功能
}

/**
 * 处理指标点击
 */
const handleMetricClick = (metric: MetricItem) => {
  console.log('点击指标:', metric.title)
  // 这里可以实现指标详情查看功能
}

/**
 * 处理图表类型变化
 */
const handleChartTypeChange = (chartType: string) => {
  console.log('图表类型变更为:', chartType)
}

/**
 * 实时数据更新定时器
 */
let realtimeUpdateInterval: NodeJS.Timeout
let isUpdating = ref(false)

/**
 * 防抖的数据更新函数
 * 避免频繁更新导致的性能问题
 */
const updateRealtimeData = createDebounce(() => {
  if (isUpdating.value) return

  isUpdating.value = true
  performanceMonitor.markRenderStart()

  // 使用批量更新优化性能
  const updates = [
    () => {
      // 批量更新数据，减少响应式触发次数
      const transactionValue = Math.floor(Math.random() * 100)
      const alertValue = Math.floor(Math.random() * 20)

      realtimeTransactionData.value.datasets[0].data = [transactionValue]
      realtimeAlertData.value.datasets[0].data = [alertValue]
    },
    () => {
      // 更新关键指标（降低频率）
      if (Math.random() < 0.2) {
        keyMetrics.value[0].value = `¥${(Math.random() * 5 + 10).toFixed(1)}M`
        keyMetrics.value[1].value = Math.floor(Math.random() * 30 + 15).toString()
        keyMetrics.value[2].value = Math.floor(Math.random() * 500 + 1500).toString()
        keyMetrics.value[3].value = `${(Math.random() * 1 + 2).toFixed(1)}TB`
      }
    }
  ]

  try {
    batchUpdate(updates)

    nextTick(() => {
      const renderTime = performanceMonitor.markRenderEnd()
      if (renderTime > 16) {
        console.warn(`数据更新渲染时间过长: ${renderTime.toFixed(2)}ms`)
      }
      isUpdating.value = false
    })
  } catch (error) {
    console.warn('实时数据更新失败:', error)
    isUpdating.value = false
  }
}, 1000) // 1秒防抖

/**
 * 生命周期钩子
 */
onMounted(() => {
  console.log('数据可视化组件已挂载')

  // 启动实时数据更新 - 降低频率从3秒到8秒
  realtimeUpdateInterval = setInterval(updateRealtimeData, 8000)

  // 初始化数据
  updateRealtimeData()

  // 开发环境下显示性能信息
  if (import.meta.env.DEV) {
    setTimeout(() => {
      const report = performanceMonitor.getPerformanceReport()
      console.log('📊 数据可视化性能报告:', report)
    }, 30000) // 30秒后显示报告
  }
})

onUnmounted(() => {
  console.log('数据可视化组件已卸载')

  // 清理定时器
  if (realtimeUpdateInterval) {
    clearInterval(realtimeUpdateInterval)
  }

  // 重置状态
  isUpdating.value = false

  // 开发环境下显示最终性能报告
  if (import.meta.env.DEV) {
    const report = performanceMonitor.getPerformanceReport()
    console.log('📈 组件卸载时性能报告:', report)
  }
})
</script>

<style scoped>
/* 强制保持4列布局 - 关键指标卡片 */
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
}

/* 其他网格的响应式布局 */
@media (max-width: 1536px) {
  .grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 1024px) {
  .grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

@media (max-width: 768px) {
  /* 小屏幕时关键指标改为2列 */
  .grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }
}

@media (max-width: 480px) {
  /* 超小屏幕时关键指标改为1列 */
  .grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }
}

/* 4K分辨率优化 */
@media (min-width: 3840px) {
  .grid-cols-3 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}
</style>
