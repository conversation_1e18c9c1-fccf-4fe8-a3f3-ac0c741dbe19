<script setup lang="ts">
import { ref } from 'vue'
import AppSidebar from '@/components/AppSidebar.vue'
import Dashboard from '@/components/Dashboard.vue'
import DataVisualizationDashboard from '@/components/DataVisualizationDashboard.vue'
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar'

/**
 * 当前活动视图类型
 */
type ViewType = 'dashboard' | 'data-visualization'

/**
 * 当前活动视图
 */
const currentView = ref<ViewType>('dashboard')

/**
 * 切换视图
 * @param view 目标视图类型
 */
const switchView = (view: ViewType) => {
  currentView.value = view
  console.log('切换到视图:', view)
}
</script>

<template>
  <SidebarProvider>
    <AppSidebar @view-change="switchView" :current-view="currentView" />
    <SidebarInset>
      <!-- 银行监控告警系统主仪表盘 -->
      <Dashboard v-if="currentView === 'dashboard'" />

      <!-- 数据可视化页面 -->
      <DataVisualization v-else-if="currentView === 'data-visualization'" />
    </SidebarInset>
  </SidebarProvider>
</template>
